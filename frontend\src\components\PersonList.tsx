import React, { useState } from "react";
import { EditPersonModal } from "./EditPersonModal";
import { UploadProfileModal } from "./UploadProfileModal";
import ImageEditorModal from "./ImageEditor/ImageEditorModal";
import {
  MasterTable,
  TableData,
  ColumnConfig,
  TableActionConfig,
} from "./Table";
import { personService, Person } from "../services/personService";
import { useNotification } from "../contexts/NotificationContext";
import { Icon } from "@iconify/react";
import { EditablePersonAvatar } from "./common";

// const API_BASE_URL =
//   process.env.REACT_APP_API_URL || "https://print-api.webstudiomatrix.com";

interface PersonListProps {
  persons: Person[];
  loading: boolean;
  onPersonSelect?: (person: Person) => void;
  onRefresh: () => void;
  onPersonDelete?: (person: Person) => void;
}

// Transform Person to TableData format
const transformPersonToTableData = (person: Person): TableData<Person> => ({
  _id: person.id.toString(),
  ...person,
});

export const PersonList: React.FC<PersonListProps> = ({
  persons,
  loading,
  onPersonSelect,
  onRefresh,
  onPersonDelete,
}) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [filterType, setFilterType] = useState<
    "all" | "student" | "staff" | "non_teaching"
  >("all");
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");

  // Modal states
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [uploadModalOpen, setUploadModalOpen] = useState(false);
  const [imageEditorOpen, setImageEditorOpen] = useState(false);
  const [selectedPerson, setSelectedPerson] = useState<Person | null>(null);
  const [selectedImageFile, setSelectedImageFile] = useState<File | null>(null);
  const [deleting, setDeleting] = useState<number | null>(null);

  const { showNotification } = useNotification();

  // Modal handlers
  const handleEditPerson = (person: Person) => {
    setSelectedPerson(person);
    setEditModalOpen(true);
  };

  const handleUploadProfile = (person: Person) => {
    setSelectedPerson(person);
    setUploadModalOpen(true);
  };

  const handleEditExistingImage = async (person: Person) => {
    if (!person.photo_path) {
      // If no image exists, open upload modal instead
      handleUploadProfile(person);
      return;
    }

    try {
      // Fetch the existing image as a blob and convert to File
      const API_BASE_URL =
        process.env.REACT_APP_API_URL ||
        "https://print-api.webstudiomatrix.com";
      const response = await fetch(
        `${API_BASE_URL}/api/cards/photos/${person.photo_path}?t=${Date.now()}`
      );

      if (!response.ok) {
        throw new Error("Failed to fetch image");
      }

      const blob = await response.blob();
      const file = new File([blob], `${person.person_id}_photo.jpg`, {
        type: "image/jpeg",
      });

      setSelectedPerson(person);
      setSelectedImageFile(file);
      setImageEditorOpen(true);
    } catch (error) {
      console.error("Error fetching image for editing:", error);
      showNotification("Failed to load image for editing", "error");
      // Fallback to upload modal
      handleUploadProfile(person);
    }
  };

  const handleImageClick = (person: Person) => {
    if (person.photo_path) {
      // If image exists, open editor
      handleEditExistingImage(person);
    } else {
      // If no image, open upload modal
      handleUploadProfile(person);
    }
  };

  const handleModalClose = () => {
    setEditModalOpen(false);
    setUploadModalOpen(false);
    setImageEditorOpen(false);
    setSelectedPerson(null);
    setSelectedImageFile(null);
  };

  const handleModalSuccess = () => {
    onRefresh();
    handleModalClose();
  };

  const handleImageEditorSave = async (processedFile: File) => {
    if (!selectedPerson) return;

    try {
      const formData = new FormData();
      formData.append("photo", processedFile);

      await personService.uploadPhoto(selectedPerson.id, formData);
      showNotification("Photo updated successfully!", "success");
      handleModalSuccess();
    } catch (error: any) {
      console.error("Error updating photo:", error);
      showNotification(
        error.response?.data?.error || "Failed to update photo",
        "error"
      );
    }
  };

  const handleDeletePerson = async (person: Person) => {
    const confirmMessage = `Are you sure you want to delete ${person.name}?\n\nThis will permanently delete:\n• Person record\n• Associated photo (if any)\n\nThis action cannot be undone!`;

    if (!window.confirm(confirmMessage)) {
      return;
    }

    try {
      setDeleting(person.id);
      await personService.deletePerson(person.id);

      showNotification(`Successfully deleted ${person.name}`, "success");

      // Call the parent's delete handler if provided, otherwise refresh
      if (onPersonDelete) {
        onPersonDelete(person);
      } else {
        onRefresh();
      }
    } catch (error: any) {
      console.error("Error deleting person:", error);
      showNotification(
        error.response?.data?.error || `Failed to delete ${person.name}`,
        "error"
      );
    } finally {
      setDeleting(null);
    }
  };

  // Table configuration for MasterTable
  const tableColumns: ColumnConfig<Person>[] = [
    {
      key: "photo_path",
      header: "Photo",
      width: "80px",
      render: (value, row) => (
        <div className="flex items-center space-x-2">
          <EditablePersonAvatar
            person={row}
            size="md"
            showEditIcon={true}
            onEditClick={handleImageClick}
            editIconStyle="badge"
          />
          <div className="flex items-center text-xs">
            <span
              className={`w-2 h-2 rounded-full mr-1 ${
                value ? "bg-green-400" : "bg-gray-300"
              }`}
            />
            {value ? "✓" : "✗"}
          </div>
        </div>
      ),
    },
    {
      key: "name",
      header: "Name",
      sortable: true,
      render: (_value, row) => (
        <div>
          <div className="font-medium text-gray-900">{row.name}</div>
          <div className="text-sm text-gray-500">ID: {row.person_id}</div>
        </div>
      ),
    },
    {
      key: "type",
      header: "Type",
      sortable: true,
      render: (_value, row) => (
        <span
          className={`px-2 py-1 rounded-full text-xs font-medium ${getTypeColor(
            row.type
          )}`}
        >
          {getTypeLabel(row.type)}
        </span>
      ),
    },
    {
      key: "class",
      header: "Class/Department",
      render: (_value, row) => {
        if (row.type === "student" && row.class && row.section) {
          return `${row.class}-${row.section}`;
        }
        if (row.type !== "student" && row.department) {
          return row.department;
        }
        return "N/A";
      },
    },
    {
      key: "contact_no",
      header: "Contact",
      render: (value) => value || "N/A",
    },
    {
      key: "created_at",
      header: "Added",
      render: (value) => new Date(value).toLocaleDateString(),
    },
  ];

  const tableActions: TableActionConfig<Person>[] = [
    {
      type: "edit",
      icon: "lucide:pen-line",
      color: "#F6B827",
      title: "Edit Person",
      onClick: (row) => handleEditPerson(row),
    },
    {
      type: "custom",
      icon: "mdi:camera-plus",
      color: "#10B981",
      title: "Photo",
      onClick: (row) => handleImageClick(row),
    },
    {
      type: "delete",
      icon: "mdi:delete",
      color: "#EF4444",
      title: "Delete Person",
      onClick: (row) => handleDeletePerson(row),
      disabled: (row) => deleting === row.id,
    },
  ];

  const filteredPersons = persons.filter((person) => {
    const matchesSearch =
      person.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      person.person_id
        .toString()
        .toLowerCase()
        .includes(searchTerm.toLowerCase());
    const matchesType = filterType === "all" || person.type === filterType;
    return matchesSearch && matchesType;
  });

  const getTypeColor = (type: string) => {
    switch (type) {
      case "student":
        return "bg-birta-orange text-white";
      case "staff":
        return "bg-birta-blue text-white";
      case "non_teaching":
        return "bg-birta-green text-white";
      default:
        return "bg-gray-500 text-white";
    }
  };

  const getTypeLabel = (type: string) => {
    switch (type) {
      case "student":
        return "Student";
      case "staff":
        return "Teaching Staff";
      case "non_teaching":
        return "Non-Teaching Staff";
      default:
        return type;
    }
  };

  return (
    <div className="space-y-6">
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-lg font-medium text-gray-900">Person List</h2>
          <div className="flex items-center space-x-3">
            {/* View Mode Toggle */}
            <div className="flex bg-gray-100 rounded-lg p-1">
              <button
                onClick={() => setViewMode("grid")}
                className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                  viewMode === "grid"
                    ? "bg-white text-gray-900 shadow-sm"
                    : "text-gray-600 hover:text-gray-900"
                }`}
              >
                <svg
                  className="w-4 h-4 inline mr-1"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
                </svg>
                Grid
              </button>
              <button
                onClick={() => setViewMode("list")}
                className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                  viewMode === "list"
                    ? "bg-white text-gray-900 shadow-sm"
                    : "text-gray-600 hover:text-gray-900"
                }`}
              >
                <svg
                  className="w-4 h-4 inline mr-1"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z"
                    clipRule="evenodd"
                  />
                </svg>
                List
              </button>
            </div>
            <button
              onClick={onRefresh}
              disabled={loading}
              className="bg-gray-100 hover:bg-gray-200 text-gray-700 px-3 py-2 rounded-md text-sm disabled:opacity-50"
            >
              {loading ? "Loading..." : "Refresh"}
            </button>
          </div>
        </div>

        {/* Filters */}
        <div className="flex flex-col sm:flex-row gap-4 mb-6">
          <div className="flex-1">
            <input
              type="text"
              placeholder="Search by name or ID..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-birta-orange focus:border-transparent"
            />
          </div>
          <div>
            <select
              value={filterType}
              onChange={(e) => setFilterType(e.target.value as any)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-birta-orange focus:border-transparent"
            >
              <option value="all">All Types</option>
              <option value="student">Students</option>
              <option value="staff">Teaching Staff</option>
              <option value="non_teaching">Non-Teaching Staff</option>
            </select>
          </div>
        </div>

        {/* Results Summary */}
        <div className="mb-4 text-sm text-gray-600">
          Showing {filteredPersons.length} of {persons.length} persons
        </div>

        {/* Person List */}
        {loading ? (
          <div className="text-center py-8">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-birta-orange"></div>
            <p className="mt-2 text-gray-500">Loading persons...</p>
          </div>
        ) : filteredPersons.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-gray-500">
              {persons.length === 0
                ? "No persons found. Upload CSV data to get started."
                : "No persons match your search criteria."}
            </p>
          </div>
        ) : viewMode === "grid" ? (
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {filteredPersons.map((person) => (
              <div
                key={person.id}
                className={`border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow ${
                  onPersonSelect ? "cursor-pointer" : ""
                }`}
                onClick={() => onPersonSelect?.(person)}
              >
                <div className="flex items-start space-x-3 mb-3">
                  <EditablePersonAvatar
                    person={person}
                    size="lg"
                    showEditIcon={true}
                    onEditClick={handleImageClick}
                    editIconStyle="badge"
                  />
                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between">
                      <div>
                        <h3 className="font-medium text-gray-900">
                          {person.name}
                        </h3>
                        <p className="text-sm text-gray-500">
                          ID: {person.person_id}
                        </p>
                      </div>
                      <span
                        className={`px-2 py-1 rounded-full text-xs font-medium ${getTypeColor(
                          person.type
                        )}`}
                      >
                        {getTypeLabel(person.type)}
                      </span>
                    </div>
                  </div>
                </div>

                <div className="space-y-1 text-sm text-gray-600">
                  {person.type === "student" && (
                    <>
                      {person.class && (
                        <p>
                          Class: {person.class}{" "}
                          {person.section && `- ${person.section}`}
                        </p>
                      )}
                      {person.roll_number && (
                        <p>Roll No: {person.roll_number}</p>
                      )}
                      {person.parents_name && (
                        <p>Parent: {person.parents_name}</p>
                      )}
                    </>
                  )}
                  {person.contact_no && <p>Contact: {person.contact_no}</p>}
                </div>

                <div className="mt-3 flex items-center justify-between">
                  <div className="flex items-center text-xs text-gray-400">
                    <span
                      className={`w-2 h-2 rounded-full mr-2 ${
                        person.photo_path ? "bg-green-400" : "bg-gray-300"
                      }`}
                    ></span>
                    {person.photo_path ? "Photo uploaded" : "No photo"}
                  </div>
                  {onPersonSelect && (
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleEditPerson(person);
                        }}
                        className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                      >
                        Edit
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleImageClick(person);
                        }}
                        className="text-green-600 hover:text-green-800 text-sm font-medium"
                      >
                        {person.photo_path ? "Edit Photo" : "Upload Photo"}
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDeletePerson(person);
                        }}
                        disabled={deleting === person.id}
                        className="text-red-600 hover:text-red-800 text-sm font-medium disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {deleting === person.id ? "Deleting..." : "Delete"}
                      </button>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        ) : (
          <MasterTable
            data={filteredPersons.map(transformPersonToTableData)}
            columns={tableColumns}
            loading={loading}
            actions={tableActions}
            showSelectAll={false}
            showActions={true}
            onRowClick={
              onPersonSelect ? (row) => onPersonSelect(row) : undefined
            }
            emptyStateMessage="No persons match your search criteria"
            emptyStateIcon="mdi:account-search"
            sortable={true}
            className="mt-4"
          />
        )}
      </div>

      {/* Modals */}
      <EditPersonModal
        person={selectedPerson}
        isOpen={editModalOpen}
        onClose={handleModalClose}
        onSuccess={handleModalSuccess}
      />

      <UploadProfileModal
        person={selectedPerson}
        isOpen={uploadModalOpen}
        onClose={handleModalClose}
        onSuccess={handleModalSuccess}
      />

      {/* Image Editor Modal */}
      {selectedImageFile && (
        <ImageEditorModal
          file={selectedImageFile}
          isOpen={imageEditorOpen}
          onClose={handleModalClose}
          onSave={handleImageEditorSave}
          aspectRatio={1} // Square aspect ratio for profile photos
          maxWidth={800}
          maxHeight={800}
        />
      )}
    </div>
  );
};
