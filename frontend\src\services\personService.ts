import { apiService, ApiResponse } from "./apiService";
import { DebouncedRequestManager, BatchRequestManager } from "./requestQueue";

/**
 * Person service with optimized API calls and caching
 */
export class PersonService {
  private debouncedManager: DebouncedRequestManager;
  private batchManager: BatchRequestManager;

  constructor() {
    this.debouncedManager = new DebouncedRequestManager(300); // 300ms debounce
    this.batchManager = new BatchRequestManager(100, 10); // 100ms timeout, max 10 requests
  }

  /**
   * Get all persons with caching and pagination
   */
  async getPersons(
    params: GetPersonsParams = {}
  ): Promise<ApiResponse<GetPersonsResponse>> {
    const cacheKey = `persons:${JSON.stringify(params)}`;

    return apiService.get<GetPersonsResponse>("/api/persons", {
      params,
      cache: true,
      cacheTTL: 300, // 5 minutes cache
      cacheKey,
    });
  }

  /**
   * Search persons with debouncing
   */
  searchPersons(
    query: string,
    params: Omit<GetPersonsParams, "search"> = {},
    onResults: (results: ApiResponse<GetPersonsResponse>) => void
  ): void {
    const searchKey = `search:${JSON.stringify(params)}`;

    const debouncedSearch = this.debouncedManager.debounce(
      searchKey,
      async (searchQuery: string) => {
        try {
          const results = await this.getPersons({
            ...params,
            search: searchQuery,
          });
          onResults(results);
        } catch (error) {
          console.error("Search error:", error);
        }
      },
      300 // 300ms debounce
    );

    debouncedSearch(query);
  }

  /**
   * Get single person by ID with caching
   */
  async getPerson(id: number): Promise<ApiResponse<Person>> {
    return apiService.get<Person>(`/api/persons/${id}`, {
      cache: true,
      cacheTTL: 600, // 10 minutes cache for individual records
      cacheKey: `person:${id}`,
    });
  }

  /**
   * Get multiple persons by IDs using batch requests
   */
  async getPersonsBatch(ids: number[]): Promise<Person[]> {
    const batchKey = "persons-batch";

    const results = await Promise.all(
      ids.map((id) =>
        this.batchManager.add(batchKey, id, async (personIds: number[]) => {
          // Make single request for all IDs
          const response = await apiService.post<Person[]>(
            "/api/persons/batch",
            {
              personIds,
            }
          );
          return response.data;
        })
      )
    );

    return results;
  }

  /**
   * Create new person
   */
  async createPerson(
    personData: CreatePersonData
  ): Promise<ApiResponse<Person>> {
    const response = await apiService.post<Person>("/api/persons", personData, {
      dedupe: true, // Prevent duplicate submissions
    });

    // Invalidate related cache
    apiService.clearCache("persons:*");

    return response;
  }

  /**
   * Update person
   */
  async updatePerson(
    id: number,
    personData: UpdatePersonData
  ): Promise<ApiResponse<Person>> {
    const response = await apiService.put<Person>(
      `/api/persons/${id}`,
      personData
    );

    // Invalidate related cache
    apiService.clearCache(`person:${id}`);
    apiService.clearCache("persons:*");

    return response;
  }

  /**
   * Delete person
   */
  async deletePerson(id: number): Promise<ApiResponse<void>> {
    const response = await apiService.delete<void>(`/api/persons/${id}`);

    // Invalidate related cache
    apiService.clearCache(`person:${id}`);
    apiService.clearCache("persons:*");

    return response;
  }

  /**
   * Bulk delete persons
   */
  async deletePersonsBulk(
    ids: number[]
  ): Promise<ApiResponse<BulkDeleteResponse>> {
    const response = await apiService.post<BulkDeleteResponse>(
      "/api/persons/bulk-delete",
      {
        personIds: ids,
      }
    );

    // Invalidate related cache
    ids.forEach((id) => apiService.clearCache(`person:${id}`));
    apiService.clearCache("persons:*");

    return response;
  }

  /**
   * Delete all persons for a specific school
   */
  async deleteAllSchoolData(schoolId: number): Promise<
    ApiResponse<{
      message: string;
      deletedCount: number;
      deletedPhotos: number;
      schoolName: string;
    }>
  > {
    const response = await apiService.delete<{
      message: string;
      deletedCount: number;
      deletedPhotos: number;
      schoolName: string;
    }>(`/api/persons/school/${schoolId}`);

    // Invalidate all persons cache
    apiService.clearCache("persons:*");
    apiService.clearCache("person:*");

    return response;
  }

  /**
   * Upload CSV file with progress tracking
   */
  async uploadCsv(
    file: File,
    schoolId?: number,
    onProgress?: (progress: number) => void
  ): Promise<ApiResponse<CsvUploadResponse>> {
    const additionalData: Record<string, any> = {};
    if (schoolId) {
      additionalData.school_id = schoolId.toString();
    }

    const response = await apiService.uploadFile<CsvUploadResponse>(
      "/api/persons/upload-csv",
      file,
      {
        onProgress,
        additionalData,
        fieldName: "csv",
      }
    );

    // Invalidate persons cache after successful upload
    apiService.clearCache("persons:*");

    return response;
  }

  /**
   * Get person statistics with caching
   */
  async getPersonStats(schoolId?: number): Promise<ApiResponse<PersonStats>> {
    const params = schoolId ? { school_id: schoolId } : {};

    return apiService.get<PersonStats>("/api/persons/stats", {
      params,
      cache: true,
      cacheTTL: 180, // 3 minutes cache for stats
      cacheKey: `person-stats:${schoolId || "all"}`,
    });
  }

  /**
   * Export persons data
   */
  async exportPersons(params: ExportPersonsParams = {}): Promise<Blob> {
    const response = await fetch(
      `${apiService["baseURL"]}/api/persons/export?${new URLSearchParams(
        params as any
      )}`,
      {
        method: "GET",
        credentials: "include",
        headers: {
          Authorization: `Bearer ${localStorage.getItem("accessToken")}`,
        },
      }
    );

    if (!response.ok) {
      throw new Error("Export failed");
    }

    return response.blob();
  }

  /**
   * Upload photo for a specific person
   */
  async uploadPhoto(
    personId: number,
    formData: FormData,
    onProgress?: (progress: number) => void
  ): Promise<ApiResponse<Person>> {
    const response = await apiService.uploadFile<Person>(
      `/api/persons/${personId}/photo`,
      formData.get("photo") as File,
      { onProgress, fieldName: "photo" }
    );

    // Invalidate related cache
    apiService.clearCache(`person:${personId}`);
    apiService.clearCache("persons:*");

    return response;
  }

  /**
   * Get person photo URL with fallback
   */
  getPersonPhotoUrl(person: Person): string {
    if (person.photo_path) {
      // Use the proper API endpoint with CORS headers instead of direct uploads path
      return `${apiService.getBaseURL()}/api/cards/photos/${person.photo_path}`;
    }

    // Return default avatar based on person type
    switch (person.type) {
      case "student":
        return "/images/default-student-avatar.png";
      case "staff":
        return "/images/default-staff-avatar.png";
      case "non_teaching":
        return "/images/default-nonteaching-avatar.png";
      default:
        return "/images/default-avatar.png";
    }
  }

  /**
   * Validate person data
   */
  validatePersonData(
    data: CreatePersonData | UpdatePersonData
  ): ValidationResult {
    const errors: string[] = [];

    if (!data.name || data.name.trim().length === 0) {
      errors.push("Name is required");
    }

    if (
      !data.type ||
      !["student", "staff", "non_teaching"].includes(data.type)
    ) {
      errors.push("Valid person type is required");
    }

    // Person ID is now optional - will be auto-generated if not provided
    if (data.person_id !== undefined && data.person_id <= 0) {
      errors.push("Person ID must be a positive number if provided");
    }

    if (data.type === "student") {
      if (!data.class) {
        errors.push("Class is required for students");
      }
      // Roll number is now optional for students
    }

    if (data.type === "staff" || data.type === "non_teaching") {
      if (!data.department) {
        errors.push("Department is required for staff");
      }
    }

    if (data.contact_no && !/^[\+]?[1-9][\d]{0,15}$/.test(data.contact_no)) {
      errors.push("Invalid contact number format");
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * Clear all caches
   */
  clearCache(): void {
    apiService.clearCache("person*");
  }

  /**
   * Get service statistics
   */
  getStats(): PersonServiceStats {
    return {
      cache: apiService.getCacheStats(),
      debounced: this.debouncedManager.getPendingKeys(),
      batch: this.batchManager.getStats(),
    };
  }

  /**
   * Cleanup resources
   */
  destroy(): void {
    this.debouncedManager.destroy();
    this.batchManager.clear();
  }
}

// Types
export interface Person {
  id: number;
  person_id: number;
  name: string;
  type: "student" | "staff" | "non_teaching";
  class?: string;
  section?: string;
  roll_number?: string;
  parents_name?: string;
  contact_no?: string;
  photo_path?: string;
  department?: string;
  designation?: string;
  address?: string;
  date_of_birth?: string;
  school_id?: number;
  csv_batch_id?: number;
  created_at: string;
  updated_at: string;
  School?: {
    id: number;
    name: string;
    address: string;
    phone: string;
    email?: string;
  };
}

export interface GetPersonsParams {
  page?: number;
  limit?: number;
  search?: string;
  type?: "student" | "staff" | "non_teaching";
  school_id?: number;
  class?: string;
  section?: string;
  department?: string;
}

export interface GetPersonsResponse {
  persons: Person[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

export interface CreatePersonData {
  name: string;
  type: "student" | "staff" | "non_teaching";
  person_id?: number; // Made optional - will be auto-generated if not provided
  class?: string;
  section?: string;
  roll_number?: string;
  parents_name?: string;
  contact_no?: string;
  department?: string;
  designation?: string;
  address?: string;
  date_of_birth?: string;
  school_id?: number;
}

export interface UpdatePersonData extends Partial<CreatePersonData> {}

export interface BulkDeleteResponse {
  deleted: number;
  failed: number;
  errors?: string[];
}

export interface CsvUploadResponse {
  message: string;
  processed: number;
  errors?: string[];
  batch_id: number;
}

export interface PersonStats {
  total: number;
  breakdown: {
    student: number;
    staff: number;
    non_teaching: number;
  };
  by_school?: Record<string, number>;
}

export interface ExportPersonsParams {
  type?: "student" | "staff" | "non_teaching";
  school_id?: number;
  format?: "csv" | "xlsx";
}

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

export interface PersonServiceStats {
  cache: any;
  debounced: string[];
  batch: any;
}

// Create singleton instance
export const personService = new PersonService();
