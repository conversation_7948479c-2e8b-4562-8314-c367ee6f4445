/**
 * Nepali Calendar (<PERSON><PERSON><PERSON>) utilities
 * Conversion between AD and BS dates using nepali-date library
 */

import NepaliDate from "nepali-date";

/**
 * Convert AD date to BS date
 */
export const adToBS = (
  adDate: Date
): { year: number; month: number; day: number } => {
  try {
    const nepaliDate = new NepaliDate(adDate);
    return {
      year: nepaliDate.getYear(),
      month: nepaliDate.getMonth() + 1, // nepali-date uses 0-based months
      day: nepaliDate.getDate(),
    };
  } catch (error) {
    console.error("Error converting AD to BS:", error);
    // Fallback to current date
    const today = new NepaliDate();
    return {
      year: today.getYear(),
      month: today.getMonth() + 1,
      day: today.getDate(),
    };
  }
};

/**
 * Convert BS date to AD date
 */
export const bsToAD = (
  bsYear: number,
  bsMonth: number,
  bsDay: number
): Date => {
  try {
    const nepaliDate = new NepaliDate();
    nepaliDate.setY<PERSON>(bsYear);
    nepaliDate.setMonth(bsMonth - 1); // nepali-date uses 0-based months
    nepaliDate.setDate(bsDay);
    return nepaliDate.getEnglishDate();
  } catch (error) {
    console.error("Error converting BS to AD:", error);
    // Fallback to current date
    return new Date();
  }
};

/**
 * Format BS date as YYYY/MM/DD
 */
export const formatBSDate = (
  bsYear: number,
  bsMonth: number,
  bsDay: number
): string => {
  return `${bsYear}/${String(bsMonth).padStart(2, "0")}/${String(
    bsDay
  ).padStart(2, "0")}`;
};

/**
 * Parse BS date string (YYYY/MM/DD or YYYY-MM-DD)
 */
export const parseBSDate = (
  dateString: string
): { year: number; month: number; day: number } | null => {
  const parts = dateString.split(/[\/\-]/);
  if (parts.length !== 3) return null;

  const year = parseInt(parts[0]);
  const month = parseInt(parts[1]);
  const day = parseInt(parts[2]);

  if (isNaN(year) || isNaN(month) || isNaN(day)) return null;
  if (month < 1 || month > 12) return null;
  if (day < 1 || day > 32) return null;

  return { year, month, day };
};

/**
 * Get current BS date
 */
export const getCurrentBSDate = (): {
  year: number;
  month: number;
  day: number;
} => {
  return adToBS(new Date());
};

/**
 * Validate BS date
 */
export const isValidBSDate = (
  bsYear: number,
  bsMonth: number,
  bsDay: number
): boolean => {
  if (bsMonth < 1 || bsMonth > 12) return false;
  if (bsDay < 1 || bsDay > 32) return false;

  try {
    // Try to create a NepaliDate object to validate
    const nepaliDate = new NepaliDate();
    nepaliDate.setYear(bsYear);
    nepaliDate.setMonth(bsMonth - 1); // 0-based month
    nepaliDate.setDate(bsDay);

    // If creation succeeds, check if the date components match
    return (
      nepaliDate.getYear() === bsYear &&
      nepaliDate.getMonth() + 1 === bsMonth &&
      nepaliDate.getDate() === bsDay
    );
  } catch (error) {
    return false;
  }
};

/**
 * Get days in a BS month
 */
export const getBSDaysInMonth = (bsYear: number, bsMonth: number): number => {
  try {
    // Try different day values to find the maximum valid day for the month
    for (let day = 32; day >= 28; day--) {
      if (isValidBSDate(bsYear, bsMonth, day)) {
        return day;
      }
    }
    // Fallback to 30 days if no valid day found
    return 30;
  } catch (error) {
    // Fallback to 30 days if calculation fails
    return 30;
  }
};

/**
 * Nepali month names
 */
export const nepaliMonthNames = [
  "बैशाख",
  "जेठ",
  "आषाढ",
  "श्रावण",
  "भाद्र",
  "आश्विन",
  "कार्तिक",
  "मंसिर",
  "पौष",
  "माघ",
  "फाल्गुन",
  "चैत्र",
];

/**
 * English month names for BS
 */
export const englishBSMonthNames = [
  "Baisakh",
  "Jestha",
  "Ashadh",
  "Shrawan",
  "Bhadra",
  "Ashwin",
  "Kartik",
  "Mangsir",
  "Poush",
  "Magh",
  "Falgun",
  "Chaitra",
];
