import React, { useState, useRef } from "react";
import axios from "axios";
import { Icon } from "@iconify/react/dist/iconify.js";
import { useSchool } from "../contexts/SchoolContext";
import { CreatePersonData } from "../services/personService";
import { NepaliDatePicker } from "./common/NepaliDatePicker";

interface PersonUploadProps {
  onUploadSuccess: () => void;
  selectedSchoolId?: number;
}

interface UploadResult {
  success: boolean;
  processed: number;
  errors: string[];
  batchId?: number;
}

interface ManualFormData {
  name: string;
  type: "student" | "staff" | "non_teaching";
  person_id?: string; // Made optional
  class: string;
  section?: string;
  roll_number?: string; // Made optional
  parents_name?: string;
  contact_no: string;
  department: string;
  designation: string;
  address?: string;
  date_of_birth?: string;
}

type TabType = "csv" | "manual";

const API_BASE_URL =
  process.env.REACT_APP_API_URL || "https://print-api.webstudiomatrix.com";

export const PersonUpload: React.FC<PersonUploadProps> = ({
  onUploadSuccess,
  selectedSchoolId,
}) => {
  const { selectedSchool } = useSchool();

  // Tab state
  const [activeTab, setActiveTab] = useState<TabType>("csv");

  // CSV upload states
  const [csvFile, setCsvFile] = useState<File | null>(null);
  const [uploading, setUploading] = useState(false);
  const [uploadResult, setUploadResult] = useState<UploadResult | null>(null);
  const [dragActive, setDragActive] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Manual form states
  const [manualFormData, setManualFormData] = useState<ManualFormData>({
    name: "",
    type: "student",
    person_id: "",
    class: "",
    section: "",
    roll_number: "",
    parents_name: "",
    contact_no: "",
    department: "",
    designation: "",
    address: "",
    date_of_birth: "",
  });
  const [submittingManual, setSubmittingManual] = useState(false);
  const [manualResult, setManualResult] = useState<UploadResult | null>(null);

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      const file = e.dataTransfer.files[0];
      if (file.type === "text/csv" || file.name.endsWith(".csv")) {
        setCsvFile(file);
        setUploadResult(null);
      } else {
        alert("Please select a CSV file");
      }
    }
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      if (file.type === "text/csv" || file.name.endsWith(".csv")) {
        setCsvFile(file);
        setUploadResult(null);
      } else {
        alert("Please select a CSV file");
      }
    }
  };

  const handleUpload = async () => {
    if (!csvFile) return;

    const schoolId = selectedSchoolId || selectedSchool?.id;
    if (!schoolId) {
      setUploadResult({
        success: false,
        processed: 0,
        errors: ["Please select a school before uploading CSV data"],
      });
      return;
    }

    setUploading(true);
    setUploadResult(null);

    try {
      const formData = new FormData();
      formData.append("csv", csvFile);
      formData.append("school_id", schoolId.toString());

      const response = await axios.post(
        `${API_BASE_URL}/api/persons/upload-csv`,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      );

      setUploadResult(response.data);
      if (response.data.success) {
        onUploadSuccess();
        setCsvFile(null);
        if (fileInputRef.current) {
          fileInputRef.current.value = "";
        }
      }
    } catch (error: any) {
      setUploadResult({
        success: false,
        processed: 0,
        errors: [error.response?.data?.error || "Upload failed"],
      });
    } finally {
      setUploading(false);
    }
  };

  const clearFile = () => {
    setCsvFile(null);
    setUploadResult(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const handleUploadIconClick = () => {
    if (!csvFile) {
      // If no file is selected, open file picker
      fileInputRef.current?.click();
    } else {
      // If file is selected, trigger upload
      handleUpload();
    }
  };

  // Manual form handlers
  const handleManualInputChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement
    >
  ) => {
    const { name, value } = e.target;
    setManualFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleManualSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const schoolId = selectedSchoolId || selectedSchool?.id;
    if (!schoolId) {
      setManualResult({
        success: false,
        processed: 0,
        errors: ["Please select a school before adding person data"],
      });
      return;
    }

    setSubmittingManual(true);
    setManualResult(null);

    try {
      const personData: CreatePersonData = {
        ...manualFormData,
        school_id: schoolId,
        // Convert person_id from string to number if provided
        person_id:
          manualFormData.person_id && manualFormData.person_id.trim() !== ""
            ? parseInt(manualFormData.person_id, 10)
            : undefined,
      };

      // Remove person_id if it's invalid (NaN) to let backend auto-generate it
      if (personData.person_id && isNaN(personData.person_id)) {
        delete personData.person_id;
      }

      const response = await axios.post(
        `${API_BASE_URL}/api/persons`,
        personData,
        {
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      setManualResult({
        success: true,
        processed: 1,
        errors: [],
      });

      onUploadSuccess();

      // Reset form
      setManualFormData({
        name: "",
        type: "student",
        person_id: "",
        class: "",
        section: "",
        roll_number: "",
        parents_name: "",
        contact_no: "",
        department: "",
        designation: "",
        address: "",
        date_of_birth: "",
      });
    } catch (error: any) {
      setManualResult({
        success: false,
        processed: 0,
        errors: [error.response?.data?.error || "Failed to create person"],
      });
    } finally {
      setSubmittingManual(false);
    }
  };

  return (
    <div className="w-full mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="text-center">
        <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full mb-4 shadow-lg">
          <Icon icon="mdi:upload" className="h-8 w-8 text-white" />
        </div>
        <h1 className="text-2xl font-bold text-gray-900 mb-2">
          Add Person Data
        </h1>
        <p className="text-gray-600">
          Import data via CSV upload or add individual records manually
        </p>
      </div>

      {/* Tabs */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200">
        <div className="flex border-b border-gray-200">
          <button
            onClick={() => setActiveTab("csv")}
            className={`flex-1 px-6 py-4 text-sm font-medium transition-colors ${
              activeTab === "csv"
                ? "text-blue-600 border-b-2 border-blue-600 bg-blue-50"
                : "text-gray-500 hover:text-gray-700 hover:bg-gray-50"
            }`}
          >
            <div className="flex items-center justify-center space-x-2">
              <Icon icon="mdi:file-upload" className="h-5 w-5" />
              <span>CSV Upload</span>
            </div>
          </button>
          <button
            onClick={() => setActiveTab("manual")}
            className={`flex-1 px-6 py-4 text-sm font-medium transition-colors ${
              activeTab === "manual"
                ? "text-blue-600 border-b-2 border-blue-600 bg-blue-50"
                : "text-gray-500 hover:text-gray-700 hover:bg-gray-50"
            }`}
          >
            <div className="flex items-center justify-center space-x-2">
              <Icon icon="mdi:account-plus" className="h-5 w-5" />
              <span>Manual Entry</span>
            </div>
          </button>
        </div>
      </div>

      {/* School Selection Status */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        {selectedSchool || selectedSchoolId ? (
          <div className="flex items-center p-4 bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-lg">
            <div className="flex-shrink-0">
              <Icon
                icon="mdi:check-circle"
                className="h-5 w-5 text-green-500"
              />
            </div>
            <div className="ml-3 flex-1">
              <div className="flex items-center">
                <Icon
                  icon="mdi:school"
                  className="h-4 w-4 text-green-600 mr-2"
                />
                <p className="text-sm font-semibold text-green-800">
                  {selectedSchool?.name || `School ID: ${selectedSchoolId}`}
                </p>
              </div>
              <p className="text-xs text-green-600 mt-1">
                Data will be added to this school
              </p>
            </div>
          </div>
        ) : (
          <div className="flex items-center p-4 bg-gradient-to-r from-yellow-50 to-amber-50 border border-yellow-200 rounded-lg">
            <div className="flex-shrink-0">
              <Icon
                icon="mdi:alert-circle"
                className="h-5 w-5 text-yellow-500"
              />
            </div>
            <div className="ml-3">
              <p className="text-sm font-semibold text-yellow-800">
                No School Selected
              </p>
              <p className="text-xs text-yellow-600 mt-1">
                Please select a school before adding person data
              </p>
            </div>
          </div>
        )}
      </div>

      {/* Tab Content */}
      {activeTab === "csv" && (
        <>
          {/* File Upload Area */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div
              className={`relative transition-all duration-300 ${
                dragActive
                  ? "border-2 border-dashed border-blue-400 bg-blue-50"
                  : "border-2 border-dashed border-gray-300 hover:border-gray-400 hover:bg-gray-50"
              } rounded-xl p-8`}
              onDragEnter={handleDrag}
              onDragLeave={handleDrag}
              onDragOver={handleDrag}
              onDrop={handleDrop}
            >
              {!csvFile ? (
                <div className="text-center">
                  <div
                    className="mx-auto h-16 w-16 bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center mb-4 cursor-pointer hover:from-gray-200 hover:to-gray-300 transition-all duration-200"
                    onClick={() => fileInputRef.current?.click()}
                    title="Click to select CSV file"
                  >
                    <Icon icon="mdi:upload" className="h-8 w-8 text-gray-400" />
                  </div>
                  <div className="space-y-2">
                    <label htmlFor="csv-upload" className="cursor-pointer">
                      <span className="text-lg font-medium text-gray-900 hover:text-blue-600 transition-colors">
                        Drop your CSV file here or click to browse
                      </span>
                      <input
                        ref={fileInputRef}
                        id="csv-upload"
                        name="csv-upload"
                        type="file"
                        accept=".csv"
                        className="sr-only"
                        onChange={handleFileSelect}
                      />
                    </label>
                    <p className="text-sm text-gray-500">
                      Supports CSV files up to 100MB
                    </p>
                  </div>
                </div>
              ) : (
                <div className="flex items-center justify-between bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg p-4">
                  <div className="flex items-center space-x-3">
                    <div className="flex-shrink-0">
                      <Icon
                        icon="mdi:file-document"
                        className="h-8 w-8 text-green-500"
                      />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-900">
                        {csvFile.name}
                      </p>
                      <p className="text-xs text-gray-500">
                        {formatFileSize(csvFile.size)}
                      </p>
                    </div>
                  </div>
                  <button
                    onClick={clearFile}
                    className="flex-shrink-0 p-1 text-gray-400 hover:text-red-500 transition-colors"
                  >
                    <Icon icon="mdi:close" className="h-5 w-5" />
                  </button>
                </div>
              )}
            </div>

            {/* Upload Button */}
            {csvFile && (
              <div className="mt-6 flex justify-end">
                <button
                  onClick={handleUpload}
                  disabled={uploading || (!selectedSchool && !selectedSchoolId)}
                  className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-orange-500 to-orange-600 text-white rounded-lg hover:from-orange-600 hover:to-orange-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 font-medium shadow-sm"
                >
                  {uploading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Processing...
                    </>
                  ) : (
                    <>
                      <Icon
                        icon="mdi:upload"
                        className="h-4 w-4 mr-2 hover:scale-110 transition-transform duration-200"
                      />
                      Upload CSV
                    </>
                  )}
                </button>
              </div>
            )}
          </div>

          {/* Upload Result */}
          {uploadResult && (
            <div
              className={`bg-white rounded-xl shadow-sm border overflow-hidden ${
                uploadResult.success ? "border-green-200" : "border-red-200"
              }`}
            >
              <div
                className={`px-6 py-4 ${
                  uploadResult.success
                    ? "bg-gradient-to-r from-green-50 to-emerald-50"
                    : "bg-gradient-to-r from-red-50 to-rose-50"
                }`}
              >
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    {uploadResult.success ? (
                      <Icon
                        icon="mdi:check-circle"
                        className="h-6 w-6 text-green-500"
                      />
                    ) : (
                      <Icon
                        icon="mdi:alert-circle"
                        className="h-6 w-6 text-red-500"
                      />
                    )}
                  </div>
                  <div className="ml-3">
                    <h3
                      className={`text-lg font-semibold ${
                        uploadResult.success ? "text-green-800" : "text-red-800"
                      }`}
                    >
                      {uploadResult.success
                        ? "Upload Successful!"
                        : "Upload Failed"}
                    </h3>
                    <p
                      className={`text-sm ${
                        uploadResult.success ? "text-green-600" : "text-red-600"
                      }`}
                    >
                      {uploadResult.success
                        ? `Successfully processed ${uploadResult.processed} records`
                        : `${uploadResult.errors.length} error(s) encountered`}
                    </p>
                  </div>
                </div>
              </div>

              {!uploadResult.success && uploadResult.errors.length > 0 && (
                <div className="px-6 py-4 border-t border-red-200">
                  <h4 className="text-sm font-medium text-red-800 mb-2">
                    Errors:
                  </h4>
                  <ul className="space-y-1">
                    {uploadResult.errors.map((error, index) => (
                      <li
                        key={index}
                        className="text-sm text-red-700 flex items-start"
                      >
                        <span className="text-red-400 mr-2">•</span>
                        {error}
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {uploadResult.success && uploadResult.batchId && (
                <div className="px-6 py-4 border-t border-green-200">
                  <p className="text-sm text-green-700">
                    Batch ID:{" "}
                    <code className="bg-green-100 px-2 py-1 rounded text-green-800">
                      {uploadResult.batchId}
                    </code>
                  </p>
                </div>
              )}
            </div>
          )}
        </>
      )}

      {/* Manual Form Tab */}
      {activeTab === "manual" && (
        <>
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <form onSubmit={handleManualSubmit} className="space-y-6">
              {/* Person Type Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Person Type *
                </label>
                <select
                  name="type"
                  value={manualFormData.type}
                  onChange={handleManualInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  required
                >
                  <option value="student">Student</option>
                  <option value="staff">Teaching Staff</option>
                  <option value="non_teaching">Non-Teaching Staff</option>
                </select>
              </div>

              {/* Basic Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Person ID (Optional)
                    <span className="text-xs text-gray-500 ml-2">
                      Leave empty to auto-generate
                    </span>
                  </label>
                  <input
                    type="text"
                    name="person_id"
                    value={manualFormData.person_id || ""}
                    onChange={handleManualInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Enter unique person ID or leave empty to auto-generate"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Full Name *
                  </label>
                  <input
                    type="text"
                    name="name"
                    value={manualFormData.name}
                    onChange={handleManualInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Enter full name"
                    required
                  />
                </div>
              </div>

              {/* Student-specific fields */}
              {manualFormData.type === "student" && (
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Class
                    </label>
                    <input
                      type="text"
                      name="class"
                      value={manualFormData.class}
                      onChange={handleManualInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="e.g., 10"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Section{" "}
                      <span className="text-gray-400 text-xs">(Optional)</span>
                    </label>
                    <input
                      type="text"
                      name="section"
                      value={manualFormData.section || ""}
                      onChange={handleManualInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="e.g., A"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Roll Number{" "}
                      <span className="text-gray-400 text-xs">(Optional)</span>
                    </label>
                    <input
                      type="text"
                      name="roll_number"
                      value={manualFormData.roll_number || ""}
                      onChange={handleManualInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="e.g., 15"
                    />
                  </div>
                </div>
              )}

              {/* Staff-specific fields */}
              {(manualFormData.type === "staff" ||
                manualFormData.type === "non_teaching") && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Department
                    </label>
                    <input
                      type="text"
                      name="department"
                      value={manualFormData.department}
                      onChange={handleManualInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="e.g., Mathematics, Administration"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Designation
                    </label>
                    <input
                      type="text"
                      name="designation"
                      value={manualFormData.designation}
                      onChange={handleManualInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="e.g., Teacher, Principal, Clerk"
                    />
                  </div>
                </div>
              )}

              {/* Additional Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Parent's Name{" "}
                    <span className="text-gray-400 text-xs">(Optional)</span>
                  </label>
                  <input
                    type="text"
                    name="parents_name"
                    value={manualFormData.parents_name || ""}
                    onChange={handleManualInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Enter parent's/guardian's name"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Contact Number
                  </label>
                  <input
                    type="tel"
                    name="contact_no"
                    value={manualFormData.contact_no}
                    onChange={handleManualInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Enter contact number"
                  />
                </div>
              </div>

              {/* Date of Birth Field - Only for students */}
              {manualFormData.type === "student" && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Date of Birth (BS){" "}
                    <span className="text-gray-400 text-xs">(Optional)</span>
                  </label>
                  <NepaliDatePicker
                    value={manualFormData.date_of_birth || ""}
                    onChange={(bsDate, adDate) => {
                      setManualFormData((prev) => ({
                        ...prev,
                        date_of_birth: adDate, // Store AD date for backend
                      }));
                    }}
                    placeholder="Select date of birth"
                    minYear={1950}
                    maxYear={2090}
                    className="w-full"
                  />
                  <p className="mt-1 text-xs text-gray-500">
                    This will be displayed on Layout 4 ID cards for students (in
                    BS format)
                  </p>
                </div>
              )}

              {/* Address Field */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Address{" "}
                  <span className="text-gray-400 text-xs">(Optional)</span>
                </label>
                <textarea
                  name="address"
                  value={manualFormData.address || ""}
                  onChange={handleManualInputChange}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Enter person's address (if different from school address)"
                />
              </div>

              {/* Submit Button */}
              <div className="flex justify-end pt-4">
                <button
                  type="submit"
                  disabled={
                    submittingManual || (!selectedSchool && !selectedSchoolId)
                  }
                  className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-lg hover:from-blue-600 hover:to-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 font-medium shadow-sm"
                >
                  {submittingManual ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Adding Person...
                    </>
                  ) : (
                    <>
                      <Icon icon="mdi:account-plus" className="h-4 w-4 mr-2" />
                      Add Person
                    </>
                  )}
                </button>
              </div>
            </form>
          </div>

          {/* Manual Form Result */}
          {manualResult && (
            <div
              className={`bg-white rounded-xl shadow-sm border overflow-hidden ${
                manualResult.success ? "border-green-200" : "border-red-200"
              }`}
            >
              <div
                className={`px-6 py-4 ${
                  manualResult.success
                    ? "bg-gradient-to-r from-green-50 to-emerald-50"
                    : "bg-gradient-to-r from-red-50 to-rose-50"
                }`}
              >
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    {manualResult.success ? (
                      <Icon
                        icon="mdi:check-circle"
                        className="h-6 w-6 text-green-500"
                      />
                    ) : (
                      <Icon
                        icon="mdi:alert-circle"
                        className="h-6 w-6 text-red-500"
                      />
                    )}
                  </div>
                  <div className="ml-3">
                    <h3
                      className={`text-lg font-semibold ${
                        manualResult.success ? "text-green-800" : "text-red-800"
                      }`}
                    >
                      {manualResult.success
                        ? "Person Added Successfully!"
                        : "Failed to Add Person"}
                    </h3>
                    <p
                      className={`text-sm ${
                        manualResult.success ? "text-green-600" : "text-red-600"
                      }`}
                    >
                      {manualResult.success
                        ? "Person record has been created successfully"
                        : `${manualResult.errors.length} error(s) encountered`}
                    </p>
                  </div>
                </div>
              </div>

              {!manualResult.success && manualResult.errors.length > 0 && (
                <div className="px-6 py-4 border-t border-red-200">
                  <h4 className="text-sm font-medium text-red-800 mb-2">
                    Errors:
                  </h4>
                  <ul className="space-y-1">
                    {manualResult.errors.map((error, index) => (
                      <li
                        key={index}
                        className="text-sm text-red-700 flex items-start"
                      >
                        <span className="text-red-400 mr-2">•</span>
                        {error}
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default PersonUpload;
