import React, { useState, useRef } from "react";
import axios from "axios";
import { Icon } from "@iconify/react";
import { ImageEditorModal } from "./ImageEditor";
import { Person } from "../services/personService";

interface UploadProfileModalProps {
  person: Person | null;
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

const API_BASE_URL =
  process.env.REACT_APP_API_URL || "https://print-api.webstudiomatrix.com";

// Simple file validation functions
const isValidImageFile = (file: File): boolean => {
  const validTypes = [
    "image/jpeg",
    "image/jpg",
    "image/png",
    "image/gif",
    "image/webp",
  ];
  return validTypes.includes(file.type);
};

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return "0 Bytes";
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};

export const UploadProfileModal: React.FC<UploadProfileModalProps> = ({
  person,
  isOpen,
  onClose,
  onSuccess,
}) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [processedFile, setProcessedFile] = useState<File | null>(null);
  const [preview, setPreview] = useState<string | null>(null);
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [dragActive, setDragActive] = useState(false);
  const [showImageEditor, setShowImageEditor] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = (file: File) => {
    if (!isValidImageFile(file)) {
      setError("Please select an image file (JPG, PNG, GIF, WebP)");
      return;
    }

    // Check file size (100MB limit)
    if (file.size > 100 * 1024 * 1024) {
      setError("File size must be less than 100MB");
      return;
    }

    setSelectedFile(file);
    setProcessedFile(null);
    setError(null);

    console.log(`Selected file: ${file.name} (${formatFileSize(file.size)})`);

    // Create preview from original file
    const reader = new FileReader();
    reader.onload = (e) => {
      setPreview(e.target?.result as string);
    };
    reader.readAsDataURL(file);
  };

  const handleEditImage = () => {
    if (selectedFile) {
      setShowImageEditor(true);
    }
  };

  const handleImageEditorSave = (editedFile: File) => {
    setProcessedFile(editedFile);
    setShowImageEditor(false);

    // Update preview with processed file
    const reader = new FileReader();
    reader.onload = (e) => {
      setPreview(e.target?.result as string);
    };
    reader.readAsDataURL(editedFile);
  };

  const handleImageEditorClose = () => {
    setShowImageEditor(false);
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      handleFileSelect(e.target.files[0]);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFileSelect(e.dataTransfer.files[0]);
    }
  };

  const handleUpload = async () => {
    const fileToUpload = processedFile || selectedFile;
    if (!fileToUpload || !person) return;

    setUploading(true);
    setError(null);

    try {
      const formData = new FormData();
      formData.append("photo", fileToUpload);

      console.log(
        `Uploading file: ${fileToUpload.name} (${formatFileSize(
          fileToUpload.size
        )})`
      );

      await axios.post(
        `${API_BASE_URL}/api/persons/${person.id}/photo`,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      );

      onSuccess();
      onClose();
      resetForm();
    } catch (error: any) {
      setError(error.response?.data?.error || "Failed to upload profile photo");
    } finally {
      setUploading(false);
    }
  };

  const resetForm = () => {
    setSelectedFile(null);
    setProcessedFile(null);
    setPreview(null);
    setError(null);
    setShowImageEditor(false);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const handleClose = () => {
    resetForm();
    onClose();
  };

  if (!isOpen || !person) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold text-gray-900">
            Upload Profile Photo
          </h2>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-600"
          >
            ✕
          </button>
        </div>

        <div className="mb-4">
          <p className="text-sm text-gray-600">
            Uploading photo for:{" "}
            <span className="font-medium">{person.name}</span>
          </p>
          <p className="text-xs text-gray-500">ID: {person.person_id}</p>
        </div>

        {error && (
          <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
            {error}
          </div>
        )}

        {/* File Upload Area */}
        <div
          className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
            dragActive
              ? "border-blue-400 bg-blue-50"
              : "border-gray-300 hover:border-gray-400"
          }`}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          {preview ? (
            <div className="space-y-4">
              <div className="mx-auto w-32 h-32 rounded-lg overflow-hidden border-2 border-gray-200 relative">
                <img
                  src={preview}
                  alt="Preview"
                  className="w-full h-full object-cover"
                />
                {processedFile && (
                  <div className="absolute top-1 right-1 bg-green-500 text-white text-xs px-1 py-0.5 rounded">
                    <Icon icon="mdi:check" className="w-3 h-3" />
                  </div>
                )}
              </div>
              <div>
                <p className="text-sm font-medium text-gray-900">
                  {(processedFile || selectedFile)?.name}
                </p>
                <p className="text-xs text-gray-500">
                  Size:{" "}
                  {(processedFile || selectedFile) &&
                    formatFileSize((processedFile || selectedFile)!.size)}
                </p>
                {processedFile && (
                  <p className="text-xs text-green-600 flex items-center mt-1">
                    <Icon icon="mdi:image-edit" className="w-3 h-3 mr-1" />
                    Image edited
                  </p>
                )}
              </div>
              <div className="flex space-x-2">
                <button
                  onClick={handleEditImage}
                  className="flex-1 bg-blue-600 text-white px-3 py-2 rounded-md hover:bg-blue-700 text-sm font-medium flex items-center justify-center space-x-1"
                >
                  <Icon icon="mdi:image-edit" className="w-4 h-4" />
                  <span>Edit Image</span>
                </button>
                <button
                  onClick={() => fileInputRef.current?.click()}
                  className="flex-1 text-gray-600 border border-gray-300 px-3 py-2 rounded-md hover:bg-gray-50 text-sm font-medium"
                >
                  Choose Different
                </button>
              </div>
            </div>
          ) : (
            <div>
              <div className="mx-auto w-12 h-12 text-gray-400 mb-4">📷</div>
              <label htmlFor="photo-upload" className="cursor-pointer">
                <span className="mt-2 block text-sm font-medium text-gray-900">
                  Drop photo here or click to browse
                </span>
                <input
                  ref={fileInputRef}
                  id="photo-upload"
                  name="photo-upload"
                  type="file"
                  accept="image/*"
                  className="sr-only"
                  onChange={handleFileInputChange}
                />
              </label>
              <p className="mt-1 text-xs text-gray-500">
                PNG, JPG, GIF up to 100MB
              </p>
            </div>
          )}
        </div>

        {/* Action Buttons */}
        <div className="flex justify-end space-x-3 mt-6">
          <button
            onClick={handleClose}
            className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
            disabled={uploading}
          >
            Cancel
          </button>
          <button
            onClick={handleUpload}
            disabled={!selectedFile || uploading}
            className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
          >
            {uploading ? (
              <>
                <Icon icon="mdi:loading" className="w-4 h-4 animate-spin" />
                <span>Uploading...</span>
              </>
            ) : (
              <>
                <Icon icon="mdi:upload" className="w-4 h-4" />
                <span>Upload Photo</span>
              </>
            )}
          </button>
        </div>
      </div>

      {/* Image Editor Modal */}
      {selectedFile && (
        <ImageEditorModal
          file={selectedFile}
          isOpen={showImageEditor}
          onClose={handleImageEditorClose}
          onSave={handleImageEditorSave}
          aspectRatio={1} // Square aspect ratio for profile photos
          maxWidth={800}
          maxHeight={800}
        />
      )}
    </div>
  );
};
