declare module "nepali-date" {
  class NepaliDate {
    timestamp: Date;
    year: number;
    month: number; // 0-based month
    day: number;

    constructor();
    constructor(date: Date);
    constructor(year: number, month: number, day: number);

    setEnglishDate(date: Date): void;
    getEnglishDate(): Date;
    parse(dateString: string): void;

    getYear(): number;
    getMonth(): number; // 0-based month
    getDate(): number;
    getDay(): number;
    getHours(): number;
    getMinutes(): number;
    getSeconds(): number;
    getMilliseconds(): number;
    getTime(): number;

    setYear(year: number): void;
    setMonth(month: number): void; // 0-based month
    setDate(date: number): void;
    set(year: number, month: number, day: number): void;

    format(format: string): string;
    toString(): string;
  }

  export = NepaliDate;
}
